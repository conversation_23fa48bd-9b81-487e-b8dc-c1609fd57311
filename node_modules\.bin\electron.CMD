@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\projects\ruler-overlay\node_modules\.pnpm\electron@32.3.3\node_modules\electron\node_modules;D:\projects\ruler-overlay\node_modules\.pnpm\electron@32.3.3\node_modules;D:\projects\ruler-overlay\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\projects\ruler-overlay\node_modules\.pnpm\electron@32.3.3\node_modules\electron\node_modules;D:\projects\ruler-overlay\node_modules\.pnpm\electron@32.3.3\node_modules;D:\projects\ruler-overlay\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\electron\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\electron\cli.js" %*
)
