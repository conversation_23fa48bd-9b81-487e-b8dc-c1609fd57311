<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Grid Overlay</title>
  <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'">
  <style>
    :root {
      /* Change this default or use 1/2/3 keys in the overlay to switch */
      --grid-size: 20px;
      --line-color: rgba(255, 255, 255, 0.25); /* semi-transparent lines */
      --thick-line-color: rgba(255, 255, 255, 0.4); /* for every 5th line */
    }

    html, body {
      width: 100%;
      height: 100%;
      margin: 0;
      background: transparent; /* Let Electron transparency show through */
      overflow: hidden;
      user-select: none;
      cursor: crosshair;
    }

    /* Two stacked repeating-linear-gradients: vertical + horizontal.
       Add a subtle "major" line every 5 cells. */
    .grid {
      position: fixed;
      inset: 0;
      background-image:
        /* vertical minor lines */
        repeating-linear-gradient(
          to right,
          var(--line-color) 0,
          var(--line-color) 1px,
          transparent 1px,
          transparent var(--grid-size)
        ),
        /* horizontal minor lines */
        repeating-linear-gradient(
          to bottom,
          var(--line-color) 0,
          var(--line-color) 1px,
          transparent 1px,
          transparent var(--grid-size)
        ),
        /* vertical major lines every 5 cells */
        repeating-linear-gradient(
          to right,
          transparent 0,
          transparent calc(var(--grid-size) * 4),
          var(--thick-line-color) calc(var(--grid-size) * 4),
          var(--thick-line-color) calc(var(--grid-size) * 4 + 1px),
          transparent calc(var(--grid-size) * 5)
        ),
        /* horizontal major lines every 5 cells */
        repeating-linear-gradient(
          to bottom,
          transparent 0,
          transparent calc(var(--grid-size) * 4),
          var(--thick-line-color) calc(var(--grid-size) * 4),
          var(--thick-line-color) calc(var(--grid-size) * 4 + 1px),
          transparent calc(var(--grid-size) * 5)
        );
      /* Optional subtle dark tint so lines show on light backgrounds too */
      /* box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1); */
    }

    /* Tiny helper badge (top-left) to show current grid size. */
    .badge {
      position: fixed;
      top: 8px;
      left: 8px;
      padding: 4px 8px;
      font-family: system-ui, sans-serif;
      font-size: 12px;
      color: rgba(255,255,255,0.9);
      background: rgba(0,0,0,0.35);
      border-radius: 4px;
      pointer-events: none; /* never intercept clicks */
    }
  </style>
</head>
<body>
  <div class="grid" id="grid"></div>
  <div class="badge" id="badge">Grid: 20px • Ctrl+Alt+G show/hide • Ctrl+Alt+C click-through</div>
  <script src="renderer.js"></script>
</body>
</html>
