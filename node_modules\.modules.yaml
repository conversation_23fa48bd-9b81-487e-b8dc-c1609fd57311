hoistPattern:
  - '*'
hoistedDependencies:
  '@electron/get@2.0.3':
    '@electron/get': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@szmarczak/http-timer@4.0.6':
    '@szmarczak/http-timer': private
  '@types/cacheable-request@6.0.3':
    '@types/cacheable-request': private
  '@types/http-cache-semantics@4.0.4':
    '@types/http-cache-semantics': private
  '@types/keyv@3.1.4':
    '@types/keyv': private
  '@types/node@20.19.11':
    '@types/node': private
  '@types/responselike@1.0.3':
    '@types/responselike': private
  '@types/yauzl@2.10.3':
    '@types/yauzl': private
  boolean@3.2.0:
    boolean: private
  buffer-crc32@0.2.13:
    buffer-crc32: private
  cacheable-lookup@5.0.4:
    cacheable-lookup: private
  cacheable-request@7.0.4:
    cacheable-request: private
  clone-response@1.0.3:
    clone-response: private
  debug@4.4.1:
    debug: private
  decompress-response@6.0.0:
    decompress-response: private
  defer-to-connect@2.0.1:
    defer-to-connect: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  detect-node@2.1.0:
    detect-node: private
  end-of-stream@1.4.5:
    end-of-stream: private
  env-paths@2.2.1:
    env-paths: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es6-error@4.1.1:
    es6-error: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  extract-zip@2.0.1:
    extract-zip: private
  fd-slicer@1.1.0:
    fd-slicer: private
  fs-extra@8.1.0:
    fs-extra: private
  get-stream@5.2.0:
    get-stream: private
  global-agent@3.0.0:
    global-agent: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  got@11.8.6:
    got: private
  graceful-fs@4.2.11:
    graceful-fs: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  http-cache-semantics@4.2.0:
    http-cache-semantics: private
  http2-wrapper@1.0.3:
    http2-wrapper: private
  json-buffer@3.0.1:
    json-buffer: private
  json-stringify-safe@5.0.1:
    json-stringify-safe: private
  jsonfile@4.0.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  lowercase-keys@2.0.0:
    lowercase-keys: private
  matcher@3.0.0:
    matcher: private
  mimic-response@3.1.0:
    mimic-response: private
  ms@2.1.3:
    ms: private
  normalize-url@6.1.0:
    normalize-url: private
  object-keys@1.1.1:
    object-keys: private
  once@1.4.0:
    once: private
  p-cancelable@2.1.1:
    p-cancelable: private
  pend@1.2.0:
    pend: private
  progress@2.0.3:
    progress: private
  pump@3.0.3:
    pump: private
  quick-lru@5.1.1:
    quick-lru: private
  resolve-alpn@1.2.1:
    resolve-alpn: private
  responselike@2.0.1:
    responselike: private
  roarr@2.15.4:
    roarr: private
  semver-compare@1.0.0:
    semver-compare: private
  semver@6.3.1:
    semver: private
  serialize-error@7.0.1:
    serialize-error: private
  sprintf-js@1.1.3:
    sprintf-js: private
  sumchecker@3.0.1:
    sumchecker: private
  type-fest@0.13.1:
    type-fest: private
  undici-types@6.21.0:
    undici-types: private
  undici-types@7.10.0:
    undici-types: private
  universalify@0.1.2:
    universalify: private
  wrappy@1.0.2:
    wrappy: private
  yauzl@2.10.0:
    yauzl: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.15.0
pendingBuilds: []
prunedAt: Sun, 31 Aug 2025 02:55:18 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped: []
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\projects\ruler-overlay\node_modules\.pnpm
virtualStoreDirMaxLength: 60
